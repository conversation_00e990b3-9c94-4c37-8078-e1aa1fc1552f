DATABASE_URL="***********************************************************/postgres?pgbouncer=true&connect_timeout=30&pool_timeout=30"

DIRECT_URL="***********************************************************/postgres?connect_timeout=30"

# Next Auth
NEXTAUTH_SECRET="your-secret-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth (optional - get from https://discord.com/developers/applications)
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

# Google OAuth (optional - get from https://console.cloud.google.com)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Redis Configuration (Redis Cloud)
REDIS_HOST="your-redis-host.redns.redis-cloud.com"
REDIS_PORT="your-redis-port"
REDIS_USERNAME="your-redis-username"
REDIS_PASSWORD="your-redis-password"
REDIS_DB="your-database-name"

# Supabase Configuration (for file storage)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_ANON_KEY="your-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Client-side Supabase (same as above but with NEXT_PUBLIC prefix)
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"

# WebSocket URL (Optional)
NEXT_PUBLIC_WS_URL="ws://localhost:3001"

# Payment Gateway Configuration

# Vietnamese Payment Methods
# MoMo (https://developers.momo.vn/)
MOMO_PARTNER_CODE="your-momo-partner-code"
MOMO_ACCESS_KEY="your-momo-access-key"
MOMO_SECRET_KEY="your-momo-secret-key"
MOMO_ENDPOINT="https://test-payment.momo.vn"  # Use https://payment.momo.vn for production

# ZaloPay (https://docs.zalopay.vn/)
ZALOPAY_APP_ID="your-zalopay-app-id"
ZALOPAY_KEY1="your-zalopay-key1"
ZALOPAY_KEY2="your-zalopay-key2"
ZALOPAY_ENDPOINT="https://sb-openapi.zalopay.vn"  # Use https://openapi.zalopay.vn for production

# VNPay (https://sandbox.vnpayment.vn/apis/)
VNPAY_TMN_CODE="your-vnpay-tmn-code"
VNPAY_HASH_SECRET="your-vnpay-hash-secret"
VNPAY_URL="https://sandbox.vnpayment.vn/paymentv2/vpcpay.html"  # Use https://vnpayment.vn/paymentv2/vpcpay.html for production
VNPAY_API_URL="https://sandbox.vnpayment.vn/merchant_webapi/api/transaction"

# VietQR (https://www.vietqr.io/en/developer)
VIETQR_CLIENT_ID="your-vietqr-client-id"
VIETQR_API_KEY="your-vietqr-api-key"
VIETQR_ENDPOINT="https://api.vietqr.io"

# International Payment Methods
# PayPal (https://developer.paypal.com/)
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_MODE="sandbox"  # Use "live" for production
PAYPAL_WEBHOOK_ID="your-paypal-webhook-id"

# Stripe (https://stripe.com/docs/api)
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Payment Configuration
PAYMENT_SUCCESS_URL="http://localhost:3000/payments/success"
PAYMENT_CANCEL_URL="http://localhost:3000/payments/cancel"
PAYMENT_WEBHOOK_URL="http://localhost:3000/api/webhooks/payments"

# Skip env validation during build (development only)
SKIP_ENV_VALIDATION=true

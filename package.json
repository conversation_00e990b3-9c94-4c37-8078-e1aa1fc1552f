{"name": "nextjs-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.7.2", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/list": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@hookform/resolvers": "^3.10.0", "@paypal/paypal-server-sdk": "^1.1.0", "@prisma/client": "^6.1.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.57.4", "@t3-oss/env-nextjs": "^0.11.1", "@tailwindcss/postcss": "^4.1.10", "@tanstack/react-query": "^5.59.16", "@tanstack/react-query-devtools": "^5.59.16", "@trpc/client": "11.0.0-rc.553", "@trpc/next": "11.0.0-rc.553", "@trpc/react-query": "11.0.0-rc.553", "@trpc/server": "11.0.0-rc.553", "@types/crypto-js": "^4.2.2", "@types/lodash.debounce": "^4.0.9", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@xyflow/react": "^12.7.0", "axios": "^1.12.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "docx": "^9.5.1", "dotenv": "^17.2.1", "framer-motion": "^11.11.11", "ioredis": "^5.6.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.454.0", "mammoth": "^1.10.0", "moment": "^2.30.1", "next": "15.5", "next-auth": "^4.24.10", "next-themes": "^0.4.3", "paypal-rest-sdk": "^1.8.1", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "redis": "^5.6.0", "server-only": "^0.0.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "stripe": "^18.5.0", "superjson": "^2.2.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "ws": "^8.18.3", "xlsx": "^0.18.5", "zod": "^3.25.64", "zustand": "^5.0.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22.9.1", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/xlsx": "^0.0.35", "eslint": "^8.57.1", "eslint-config-next": "15.1.0", "eslint-config-prettier": "^10.1.5", "postcss": "^8.4.49", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "prisma": "^6.1.0", "tailwindcss": "^4.1.10", "tsx": "^4.19.2", "tw-animate-css": "^1.3.4", "typescript": "^5.6.3"}, "engines": {"node": ">=18.17.0"}}
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Post {
  id        Int      @id @default(autoincrement())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  authorId  String?
  author    User?    @relation("UserPosts", fields: [authorId], references: [id], onDelete: SetNull)

  @@index([name])
}

// Necessary for Next auth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For email/password authentication
  role          UserRole  @default(USER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  posts         Post[]    @relation("UserPosts")

  // React Flow room relations
  ownedFlowRooms      FlowRoom[]              @relation("FlowRoomOwner")
  flowRoomParticipant FlowRoomParticipant[]   @relation("FlowRoomParticipants")
  sentInvitations     FlowRoomInvitation[]    @relation("InvitationsSent")
  receivedInvitations FlowRoomInvitation[]    @relation("InvitationsReceived")

  // File relations
  uploadedFiles       File[]                  @relation("FileOwner")
  ownedCategories     Category[]              @relation("CategoryOwner")

  // Calendar relations
  ownedEvents         Event[]                 @relation("EventOwner")

  // Payment relations
  payments            Payment[]               @relation("PaymentUser")
}

enum UserRole {
  USER
  ADMIN
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// React Flow Collaboration Models
model FlowRoom {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  ownerId     String
  owner       User     @relation("FlowRoomOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  // Store the current flow data as JSON
  flowData    Json?    // Contains nodes, edges, and viewport

  // Collaboration settings
  isPublic    Boolean  @default(false)

  // Relations
  participants FlowRoomParticipant[]
  invitations  FlowRoomInvitation[]

  @@index([ownerId])
}

model FlowRoomParticipant {
  id       String   @id @default(cuid())
  roomId   String
  userId   String
  role     ParticipantRole @default(VIEWER)
  joinedAt DateTime @default(now())

  room     FlowRoom @relation(fields: [roomId], references: [id], onDelete: Cascade)
  user     User     @relation("FlowRoomParticipants", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([roomId, userId])
  @@index([roomId])
  @@index([userId])
}

enum ParticipantRole {
  OWNER
  EDITOR
  VIEWER
}

// Room invitation system
model FlowRoomInvitation {
  id        String   @id @default(cuid())
  roomId    String
  inviterId String
  inviteeId String?  // Null if invitation is by email to non-user
  email     String?  // Email for invitations to non-users
  status    InvitationStatus @default(PENDING)
  role      ParticipantRole @default(VIEWER)
  message   String?  // Optional invitation message
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime? // Optional expiration date

  room     FlowRoom @relation(fields: [roomId], references: [id], onDelete: Cascade)
  inviter  User     @relation("InvitationsSent", fields: [inviterId], references: [id], onDelete: Cascade)
  invitee  User?    @relation("InvitationsReceived", fields: [inviteeId], references: [id], onDelete: Cascade)

  @@unique([roomId, inviteeId])
  @@unique([roomId, email])
  @@index([inviteeId])
  @@index([email])
  @@index([status])
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

// Audit Log for persistent logging
model AuditLog {
  id            String   @id @default(cuid())
  timestamp     DateTime @default(now())
  level         String   // ERROR, WARN, INFO, DEBUG
  category      String   // WEBSOCKET, REDIS, DATABASE, etc.
  message       String
  metadata      Json?    // Additional structured data
  correlationId String?  // For tracing related events
  userId        String?  // User associated with the event
  roomId        String?  // Room associated with the event
  duration      Float?   // Operation duration in milliseconds
  error         Json?    // Error details if applicable
  createdAt     DateTime @default(now())

  @@index([timestamp])
  @@index([level])
  @@index([category])
  @@index([userId])
  @@index([roomId])
  @@index([correlationId])
  @@index([timestamp, level])
  @@index([timestamp, category])
}

// File Management Models
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  color       String?  // Hex color for UI
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  ownerId     String
  owner       User     @relation("CategoryOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  // Relations
  files       FileCategory[]

  @@unique([name, ownerId]) // Unique category name per user
  @@index([ownerId])
}

model File {
  id          String   @id @default(cuid())
  filename    String
  originalName String  // Original filename from upload
  mimeType    String
  size        Int      // File size in bytes
  storageKey  String   // Path/key in Supabase storage
  publicUrl   String?  // Public URL if file is public

  // File metadata
  isPublic    Boolean  @default(false)
  description String?

  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  ownerId     String
  owner       User     @relation("FileOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  categories  FileCategory[]

  @@index([ownerId])
  @@index([mimeType])
  @@index([createdAt])
}

// Many-to-many relation between files and categories
model FileCategory {
  id         String   @id @default(cuid())
  fileId     String
  categoryId String
  file       File     @relation(fields: [fileId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())

  @@unique([fileId, categoryId])
  @@index([fileId])
  @@index([categoryId])
}

// Calendar Models
model Event {
  id          String   @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?

  // Event type and settings
  eventType   EventType @default(APPOINTMENT)
  isAllDay    Boolean   @default(false)
  isRecurring Boolean   @default(false)
  recurrenceRule String? // RRULE format for recurring events

  // Visibility and access
  isPublic    Boolean   @default(false)
  color       String?   // Hex color for display

  // Metadata
  metadata    Json?     // Additional event data

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  ownerId     String
  owner       User      @relation("EventOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  @@index([ownerId])
  @@index([startTime])
  @@index([endTime])
  @@index([eventType])
  @@index([isPublic])
  @@index([startTime, endTime])
}

enum EventType {
  APPOINTMENT
  MEETING
  TASK
  REMINDER
  AVAILABILITY
  BLOCKED
}

// Payment Models
model Payment {
  id                String        @id @default(cuid())

  // Basic payment information
  amount            Decimal       @db.Decimal(19, 4) // Support up to 999,999,999,999,999.9999
  currency          String        @default("USD")
  description       String?

  // Payment method and provider
  paymentMethod     PaymentMethod
  provider          String        // momo, zalopay, vnpay, vietqr, paypal, stripe

  // Payment status and tracking
  status            PaymentStatus @default(PENDING)

  // External references
  externalId        String?       // Payment ID from the payment provider
  transactionId     String?       // Transaction ID from the payment provider
  orderId           String        // Internal order ID

  // Payment URLs and redirects
  paymentUrl        String?       // URL to redirect user for payment
  returnUrl         String?       // URL to return after payment
  cancelUrl         String?       // URL to return if payment is cancelled

  // Provider-specific data
  providerData      Json?         // Store provider-specific response data

  // Payment metadata
  metadata          Json?         // Additional payment information

  // Error handling
  errorCode         String?       // Error code if payment failed
  errorMessage      String?       // Error message if payment failed

  // Timestamps
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  paidAt            DateTime?     // When payment was completed
  expiresAt         DateTime?     // When payment link expires

  // Relations
  userId            String
  user              User          @relation("PaymentUser", fields: [userId], references: [id], onDelete: Cascade)

  // Payment events for audit trail
  events            PaymentEvent[]

  @@index([userId])
  @@index([status])
  @@index([paymentMethod])
  @@index([provider])
  @@index([externalId])
  @@index([orderId])
  @@index([createdAt])
  @@index([status, createdAt])
}

enum PaymentMethod {
  MOMO
  ZALOPAY
  VNPAY
  VIETQR
  PAYPAL
  STRIPE
}

enum PaymentStatus {
  PENDING       // Payment created but not yet processed
  PROCESSING    // Payment is being processed
  COMPLETED     // Payment successfully completed
  FAILED        // Payment failed
  CANCELLED     // Payment was cancelled by user
  EXPIRED       // Payment link expired
  REFUNDED      // Payment was refunded
  PARTIALLY_REFUNDED // Payment was partially refunded
}

// Payment event log for detailed tracking
model PaymentEvent {
  id          String   @id @default(cuid())
  paymentId   String
  eventType   PaymentEventType
  status      PaymentStatus?
  message     String?
  data        Json?    // Event-specific data
  createdAt   DateTime @default(now())

  payment     Payment  @relation(fields: [paymentId], references: [id], onDelete: Cascade)

  @@index([paymentId])
  @@index([eventType])
  @@index([createdAt])
  @@index([paymentId, createdAt])
}

enum PaymentEventType {
  CREATED       // Payment was created
  SUBMITTED     // Payment was submitted to provider
  PROCESSING    // Payment is being processed
  COMPLETED     // Payment completed successfully
  FAILED        // Payment failed
  CANCELLED     // Payment was cancelled
  EXPIRED       // Payment expired
  WEBHOOK       // Webhook received from provider
  REFUND_INITIATED // Refund was initiated
  REFUNDED      // Refund completed
  ERROR         // Error occurred
}

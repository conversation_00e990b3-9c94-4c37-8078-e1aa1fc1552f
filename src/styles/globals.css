@import "tailwindcss";

@theme {
  --radius: 0.5rem;

  /* Light theme colors using modern OKLCH color space */
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.145 0 0);
  --color-card: oklch(1 0 0);
  --color-card-foreground: oklch(0.145 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.145 0 0);
  --color-primary: oklch(0.205 0 0);
  --color-primary-foreground: oklch(0.985 0 0);
  --color-secondary: oklch(0.97 0 0);
  --color-secondary-foreground: oklch(0.205 0 0);
  --color-muted: oklch(0.97 0 0);
  --color-muted-foreground: oklch(0.556 0 0);
  --color-accent: oklch(0.97 0 0);
  --color-accent-foreground: oklch(0.205 0 0);
  --color-destructive: oklch(0.577 0.245 27.325);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.922 0 0);
  --color-input: oklch(0.922 0 0);
  --color-ring: oklch(0.708 0 0);

  /* Chart colors with P3 gamut */
  --color-chart-1: oklch(0.646 0.222 41.116);
  --color-chart-2: oklch(0.6 0.118 184.704);
  --color-chart-3: oklch(0.398 0.07 227.392);
  --color-chart-4: oklch(0.828 0.189 84.429);
  --color-chart-5: oklch(0.769 0.188 70.08);
}

.dark {
  /* Dark theme colors using modern OKLCH color space */
  --color-background: oklch(0.145 0 0);
  --color-foreground: oklch(0.985 0 0);
  --color-card: oklch(0.205 0 0);
  --color-card-foreground: oklch(0.985 0 0);
  --color-popover: oklch(0.205 0 0);
  --color-popover-foreground: oklch(0.985 0 0);
  --color-primary: oklch(0.922 0 0);
  --color-primary-foreground: oklch(0.205 0 0);
  --color-secondary: oklch(0.269 0 0);
  --color-secondary-foreground: oklch(0.985 0 0);
  --color-muted: oklch(0.269 0 0);
  --color-muted-foreground: oklch(0.708 0 0);
  --color-accent: oklch(0.269 0 0);
  --color-accent-foreground: oklch(0.985 0 0);
  --color-destructive: oklch(0.704 0.191 22.216);
  --color-destructive-foreground: oklch(0.985 0 0);
  --color-border: oklch(0.269 0 0);
  --color-input: oklch(0.269 0 0);
  --color-ring: oklch(0.556 0 0);

  /* Dark theme chart colors */
  --color-chart-1: oklch(0.488 0.243 264.376);
  --color-chart-2: oklch(0.696 0.17 162.48);
  --color-chart-3: oklch(0.769 0.188 70.08);
  --color-chart-4: oklch(0.627 0.265 303.9);
  --color-chart-5: oklch(0.645 0.246 16.439);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  /* Ensure dark mode transitions work properly */
  .dark {
    color-scheme: dark;
  }

  .light {
    color-scheme: light;
  }
}

/* React Flow custom styles */
/* Override React Flow default node widths to allow custom sizing */
.react-flow__node-input,
.react-flow__node-default,
.react-flow__node-output,
.react-flow__node-group {
  width: auto !important;
  min-width: auto !important;
  padding: 0 !important;
}

/* React Flow CSS variables for theme support */
:root {
  --xy-node-background-color: var(--color-card);
  --xy-node-border-color: var(--color-border);
  --xy-node-color: var(--color-card-foreground);
  --xy-node-border-radius: 0.5rem;
  --xy-node-boxshadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --xy-node-border-width: 1px;
  --xy-node-border: var(--xy-node-border-width) solid var(--xy-node-border-color);
}

/* FullCalendar custom styles */
.calendar-container .fc {
  font-family: inherit;
}

.calendar-container .fc-theme-standard td,
.calendar-container .fc-theme-standard th {
  border-color: var(--color-border);
}

.calendar-container .fc-theme-standard .fc-scrollgrid {
  border-color: var(--color-border);
}

.calendar-container .fc-col-header-cell {
  background-color: var(--color-muted);
  color: var(--color-muted-foreground);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
}

.calendar-container .fc-daygrid-day {
  background-color: var(--color-background);
}

.calendar-container .fc-daygrid-day:hover {
  background-color: var(--color-muted);
}

.calendar-container .fc-day-today {
  background-color: rgba(128, 128, 128, 0.1) !important;
}

.calendar-container .fc-event {
  border: none !important;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 2px 6px;
  margin: 1px 2px;
}

.calendar-container .fc-event:hover {
  filter: brightness(0.9);
}

.calendar-container .fc-event-title {
  font-weight: 500;
}

.calendar-container .fc-event-time {
  font-weight: 400;
  opacity: 0.8;
}

.calendar-container .fc-more-link {
  color: var(--color-primary);
  font-weight: 500;
}

.calendar-container .fc-more-link:hover {
  color: var(--color-primary);
  text-decoration: underline;
}

.calendar-container .fc-popover {
  background-color: var(--color-popover);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.calendar-container .fc-popover-header {
  background-color: var(--color-muted);
  color: var(--color-muted-foreground);
  border-bottom: 1px solid var(--color-border);
  padding: 0.5rem 0.75rem;
  font-weight: 600;
}

.calendar-container .fc-popover-body {
  padding: 0.5rem;
}

.calendar-container .fc-timegrid-slot {
  border-color: var(--color-border);
}

.calendar-container .fc-timegrid-slot-minor {
  border-color: var(--color-border);
  opacity: 0.5;
}

.calendar-container .fc-timegrid-axis {
  color: var(--color-muted-foreground);
  font-size: 0.75rem;
}

.calendar-container .fc-list-event:hover {
  background-color: var(--color-muted);
}

.calendar-container .fc-list-day-cushion {
  background-color: var(--color-muted);
  color: var(--color-muted-foreground);
  font-weight: 600;
}

/* Enhanced hover effects for time grid cells (Week/Day views) */
.calendar-container .fc-timegrid-col {
  position: relative;
}

.calendar-container .fc-timegrid-col:hover {
  background-color: var(--color-accent);
  transition: background-color 0.15s ease;
}

.calendar-container .fc-timegrid-col:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-primary);
  opacity: 0.05;
  pointer-events: none;
  z-index: 1;
  transition: opacity 0.15s ease;
}

/* Hover effect for individual time slots */
.calendar-container .fc-timegrid-slot:hover {
  background-color: var(--color-accent);
  transition: background-color 0.15s ease;
}

/* Hover effect for the main content area background (where users click to create events) */
.calendar-container .fc-timegrid-col-bg:hover {
  background-color: var(--color-accent) !important;
  transition: background-color 0.15s ease;
}

/* Hover effect for individual time slot lanes in the content area */
.calendar-container .fc-timegrid-slot-lane:hover {
  background-color: var(--color-accent) !important;
  transition: background-color 0.15s ease;
}

/* Make the content area cursor pointer with higher specificity */
.calendar-container .fc-timegrid-col-bg,
.calendar-container .fc-timegrid-slot-lane {
  cursor: pointer !important;
}

/* Additional hover effects for better coverage */
.calendar-container .fc-timegrid-col:hover .fc-timegrid-col-bg {
  background-color: var(--color-accent) !important;
  transition: background-color 0.15s ease;
}

/* Ensure hover works on the entire column area */
.calendar-container .fc-timegrid-col:hover {
  background-color: var(--color-accent) !important;
  transition: background-color 0.15s ease;
}

/* Special hover color for today's column in Day/Week views */
.calendar-container .fc-timegrid-col.fc-day-today:hover {
  background-color: rgba(128, 128, 128, 0.15) !important;
  transition: background-color 0.15s ease;
}

/* Today's column background hover */
.calendar-container .fc-timegrid-col.fc-day-today .fc-timegrid-col-bg:hover {
  background-color: rgba(128, 128, 128, 0.15) !important;
  transition: background-color 0.15s ease;
}

/* Special hover for today in Month view */
.calendar-container .fc-daygrid-day.fc-day-today:hover {
  background-color: rgba(128, 128, 128, 0.15) !important;
  transition: background-color 0.15s ease;
}

/* Today's time slots hover in Day view - since Day view only shows today, all time slots are today's */
.calendar-container .fc-timeGridDay-view .fc-timegrid-slot-lane:hover {
  background-color: rgba(128, 128, 128, 0.2) !important;
  transition: background-color 0.15s ease;
}

.calendar-container .fc-timeGridDay-view .fc-timegrid-col-bg:hover {
  background-color: rgba(128, 128, 128, 0.15) !important;
  transition: background-color 0.15s ease;
}

/* Today's column hover effects in Week view - target the actual hoverable time slots */
.calendar-container .fc-timeGridWeek-view .fc-timegrid-col.fc-day-today .fc-timegrid-col-bg:hover {
  background-color: rgba(128, 128, 128, 0.15) !important;
  transition: background-color 0.15s ease;
}

/* Target individual time slots that span across today's column */
.calendar-container .fc-timeGridWeek-view .fc-timegrid-slot-lane:hover {
  background-color: var(--color-accent) !important;
  transition: background-color 0.15s ease;
}

/* Override for time slots that intersect with today's column - use CSS positioning to detect */
.calendar-container .fc-timeGridWeek-view .fc-timegrid-slots .fc-timegrid-slot-lane:nth-child(7):hover {
  background-color: rgba(128, 128, 128, 0.2) !important;
  transition: background-color 0.15s ease;
}

/* Add opacity to today's column background to allow hover effects to show through */
.calendar-container .fc-timeGridWeek-view .fc-timegrid-col.fc-day-today {
  background-color: rgba(128, 128, 128, 0.05) !important;
  transition: background-color 0.15s ease !important;
  opacity: 0.8 !important;
}

/* Also add opacity to today's column in Day view */
.calendar-container .fc-timeGridDay-view .fc-timegrid-col.fc-day-today {
  background-color: rgba(128, 128, 128, 0.05) !important;
  transition: background-color 0.15s ease !important;
  opacity: 0.8 !important;
}

/* Force pure gray overlay color with higher specificity */
.calendar-container .fc-view .fc-timegrid-col.fc-day-today {
  background-color: rgba(128, 128, 128, 0.05) !important;
}

/* Force pure gray background for today's column background div */
.calendar-container .fc-timegrid-col.fc-day-today .fc-timegrid-col-bg {
  background-color: rgba(128, 128, 128, 0.05) !important;
}

/* Force pure gray background for today's column in Week view */
.calendar-container .fc-timeGridWeek-view .fc-timegrid-col.fc-day-today .fc-timegrid-col-bg {
  background-color: rgba(128, 128, 128, 0.05) !important;
}

/* Force pure gray background for today's column in Day view */
.calendar-container .fc-timeGridDay-view .fc-timegrid-col.fc-day-today .fc-timegrid-col-bg {
  background-color: rgba(128, 128, 128, 0.05) !important;
}



/* Enhanced hover for day grid cells (Month view) */
.calendar-container .fc-daygrid-day {
  background-color: var(--color-background);
  transition: background-color 0.15s ease;
}

.calendar-container .fc-daygrid-day:hover {
  background-color: var(--color-accent);
  transition: background-color 0.15s ease;
}

/* Hover effect for time grid day headers */
.calendar-container .fc-col-header-cell:hover {
  background-color: var(--color-muted);
  transition: background-color 0.15s ease;
}

/* Add subtle border highlight on hover */
.calendar-container .fc-timegrid-col:hover {
  border-left: 2px solid var(--color-primary);
  border-right: 2px solid var(--color-primary);
}

/* Ensure events remain visible on hover */
.calendar-container .fc-timegrid-col:hover .fc-event {
  z-index: 10;
  position: relative;
}

.calendar-container .fc-button-primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-primary-foreground);
}

.calendar-container .fc-button-primary:hover {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  opacity: 0.9;
}

.calendar-container .fc-button-primary:disabled {
  background-color: var(--color-muted);
  border-color: var(--color-muted);
  color: var(--color-muted-foreground);
}

/* Dark mode adjustments */
.dark .calendar-container .fc-event {
  color: var(--color-foreground);
}

.dark .calendar-container .fc-daygrid-day:hover {
  background-color: var(--color-muted);
}

.dark .calendar-container .fc-timegrid-col:hover {
  background-color: var(--color-accent);
}

.dark .calendar-container .fc-timegrid-slot:hover {
  background-color: var(--color-accent);
}

.dark .calendar-container .fc-popover {
  background-color: var(--color-popover);
  border-color: var(--color-border);
}

/* Cursor pointer for interactive cells */
.calendar-container .fc-timegrid-col {
  cursor: pointer;
}

.calendar-container .fc-daygrid-day {
  cursor: pointer;
}

/* Add visual feedback for clickable time slots */
.calendar-container .fc-timegrid-slot-label {
  transition: color 0.15s ease;
}

.calendar-container .fc-timegrid-col:hover .fc-timegrid-slot-label {
  color: var(--color-primary);
  font-weight: 500;
}

/* Subtle animation for better UX */
.calendar-container .fc-timegrid-col,
.calendar-container .fc-daygrid-day,
.calendar-container .fc-timegrid-slot {
  transition: all 0.15s ease;
}

/* Highlight current time slot more prominently */
.calendar-container .fc-timegrid-now-indicator-line {
  border-color: var(--color-primary);
  border-width: 2px;
}

/* Custom event styles based on type */
.calendar-container .fc-event.event-type-appointment {
  background-color: #3b82f6;
  color: white;
}

.calendar-container .fc-event.event-type-meeting {
  background-color: #10b981;
  color: white;
}

.calendar-container .fc-event.event-type-task {
  background-color: #f59e0b;
  color: white;
}

.calendar-container .fc-event.event-type-reminder {
  background-color: #8b5cf6;
  color: white;
}

.calendar-container .fc-event.event-type-availability {
  background-color: #06b6d4;
  color: white;
}

.calendar-container .fc-event.event-type-blocked {
  background-color: #ef4444;
  color: white;
}



.calendar-container .fc-event.public-event::after {
  content: '👁';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
  opacity: 0.7;
}

.dark {
  --xy-node-background-color: var(--color-card);
  --xy-node-border-color: var(--color-border);
  --xy-node-color: var(--color-card-foreground);
}

/* Theme-responsive React Flow nodes */
.react-flow__node-input,
.react-flow__node-default,
.react-flow__node-output {
  background: var(--xy-node-background-color) !important;
  border: var(--xy-node-border) !important;
  border-radius: var(--xy-node-border-radius) !important;
  box-shadow: var(--xy-node-boxshadow) !important;
  color: var(--xy-node-color) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease !important;
}

/* Selected node styling */
.react-flow__node.selected {
  box-shadow: 0 0 0 2px var(--color-primary) !important;
}

/* React Flow minimap theme support */
.react-flow__minimap {
  background: var(--color-muted) !important;
  border: 1px solid var(--color-border) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

/* React Flow controls theme support */
.react-flow__controls {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

.react-flow__controls button {
  background: var(--color-background) !important;
  border-bottom: 1px solid var(--color-border) !important;
  color: var(--color-foreground) !important;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease !important;
}

.react-flow__controls button:hover {
  background: var(--color-muted) !important;
}

/* React Flow animations */
@keyframes dash {
  to {
    stroke-dashoffset: -15;
  }
}
